---
name: "Bump version (manually)"

on:
  # can be called manually from GH webpage
  workflow_dispatch:
    inputs:
      target_branch:
        default: 'main'
        description: "Branch on which the version bump is to be done."
        required: false


jobs:
  Bump-Version:
    name: 'Update snapshot version'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: eclipse-edc/.github/.github/actions/bump-version@main
        name: Bump version
        with:
          target_branch: ${{ inputs.target_branch }}

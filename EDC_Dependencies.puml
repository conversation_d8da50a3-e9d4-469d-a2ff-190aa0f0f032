@startuml EDC Module Dependencies


!theme plain
skinparam backgroundColor white
skinparam packageStyle rectangle
skinparam component {
  BackgroundColor #FFFFFF
  BorderColor #000000
  FontSize 10
}

skinparam package {
  BackgroundColor<<core>> #E1F5FE
  BackgroundColor<<spi>> #F3E5F5
  BackgroundColor<<ext>> #E8F5E8
  BackgroundColor<<protocol>> #FFF3E0
  BorderColor #000000
  FontSize 12
}

package "Core Foundation" <<core>> {
  component [boot] as boot
  component [connector-core] as connector_core
  component [runtime-core] as runtime_core
  component [edr-store-core] as edr_store_core
  component [token-core] as token_core
  component [junit] as junit
}

package "Core Libraries" <<core>> {
  component [boot-lib] as boot_lib
  component [state-machine-lib] as state_machine_lib
  component [policy-engine-lib] as policy_engine_lib
  component [query-lib] as query_lib
  component [transform-lib] as transform_lib
  component [validator-lib] as validator_lib
  component [util-lib] as util_lib
  component [json-ld-lib] as json_ld_lib
  component [http-lib] as http_lib
  component [sql-lib] as sql_lib
}

package "Control Plane Core" <<core>> {
  component [control-plane-core] as cp_core_main
  component [control-plane-catalog] as cp_catalog
  component [control-plane-contract] as cp_contract
  component [control-plane-transfer] as cp_transfer
  component [control-plane-transform] as cp_transform
  component [control-plane-aggregate-services] as cp_services
}

package "Data Plane Core" <<core>> {
  component [data-plane-core] as dp_core_main
  component [data-plane-util] as dp_util
  component [data-plane-selector-core] as dp_selector_core
}

package "Service Provider Interfaces" <<spi>> {
  component [core-spi] as core_spi
  component [boot-spi] as boot_spi
  component [web-spi] as web_spi
  component [auth-spi] as auth_spi
  component [policy-engine-spi] as policy_spi
  component [asset-spi] as asset_spi
  component [contract-spi] as contract_spi
  component [transfer-spi] as transfer_spi
  component [data-plane-spi] as dp_spi
  component [json-ld-spi] as json_ld_spi
  component [transform-spi] as transform_spi
  component [validator-spi] as validator_spi
}

package "Common Extensions" <<ext>> {
  component [jetty-core] as jetty
  component [jersey-core] as jersey
  component [configuration-filesystem] as config_fs
  component [iam-mock] as iam_mock
  component [json-ld] as json_ld_impl
  component [sql-core] as sql_core
  component [api-observability] as api_obs
  component [version-api] as version_api
}

package "Control Plane Extensions" <<ext>> {
  component [asset-api] as asset_api
  component [contract-negotiation-api] as contract_api
  component [asset-index-sql] as asset_sql
  component [contract-negotiation-store-sql] as contract_sql
  component [management-api] as mgmt_api
}

package "Data Plane Extensions" <<ext>> {
  component [data-plane-http] as dp_http
  component [data-plane-kafka] as dp_kafka
  component [data-plane-signaling-api] as dp_signaling
}

package "Data Protocols" <<protocol>> {
  component [dsp-core] as dsp_core
  component [dsp-08] as dsp_08
  component [dsp-2024] as dsp_2024
  component [dsp-2025] as dsp_2025
}

'' Core Foundation Dependencies
boot ..> boot_spi
boot ..> boot_lib
connector_core ..> core_spi
connector_core ..> policy_spi
runtime_core ..> core_spi
runtime_core ..> http_lib
edr_store_core ..> core_spi
token_core ..> core_spi

'' Core Libraries Dependencies
boot_lib ..> boot_spi
state_machine_lib ..> core_spi
policy_engine_lib ..> policy_spi
transform_lib ..> transform_spi
validator_lib ..> validator_spi
json_ld_lib ..> json_ld_spi
http_lib ..> core_spi
sql_lib ..> core_spi

'' Control Plane Core Dependencies
cp_core_main ..> asset_spi
cp_core_main ..> contract_spi
cp_core_main ..> transfer_spi
cp_catalog ..> asset_spi
cp_contract ..> contract_spi
cp_transfer ..> transfer_spi
cp_transform ..> transform_spi
cp_services ..> asset_spi
cp_services ..> contract_spi

'' Data Plane Core Dependencies
dp_core_main ..> dp_spi
dp_util ..> dp_spi
dp_selector_core ..> dp_spi

'' Common Extensions Dependencies
jetty ..> web_spi
jersey ..> web_spi
jersey ..> jetty
config_fs ..> core_spi
iam_mock ..> auth_spi
json_ld_impl ..> json_ld_spi
sql_core ..> core_spi
api_obs ..> web_spi
api_obs ..> boot_spi
version_api ..> web_spi

'' Control Plane Extensions Dependencies
asset_api ..> web_spi
asset_api ..> asset_spi
contract_api ..> web_spi
contract_api ..> contract_spi
asset_sql ..> asset_spi
asset_sql ..> sql_core
contract_sql ..> contract_spi
contract_sql ..> sql_core
mgmt_api ..> web_spi

'' Data Plane Extensions Dependencies
dp_http ..> dp_spi
dp_kafka ..> dp_spi
dp_signaling ..> web_spi
dp_signaling ..> dp_spi

'' Protocol Dependencies
dsp_core ..> core_spi
dsp_08 ..> dsp_core
dsp_2024 ..> dsp_core
dsp_2025 ..> dsp_core

'' Key Service Provision Relationships (shown with thick arrows)
boot ==> core_spi : "provides Monitor,\nHealthCheckService"
runtime_core ==> core_spi : "provides HttpClient,\nTypeManager"
jetty ==> web_spi : "provides WebServer"
jersey ==> web_spi : "provides WebService"
iam_mock ==> auth_spi : "provides IdentityService"
json_ld_impl ==> json_ld_spi : "provides JsonLd"
sql_core ==> core_spi : "provides DataSourceRegistry"

'' API Registration Pattern (shown with dotted arrows)
api_obs -.-> jersey : "registers REST\ncontrollers"
version_api -.-> jersey : "registers REST\ncontrollers"
asset_api -.-> jersey : "registers REST\ncontrollers"
contract_api -.-> jersey : "registers REST\ncontrollers"

note top of "Core Foundation" : Foundation modules provide\ncore services and infrastructure
note top of "Service Provider Interfaces" : SPIs define contracts\nfor all implementations
note top of "Common Extensions" : Technology-specific\nimplementations
note top of "Data Protocols" : Standard protocol\nimplementations

legend right
  |= Symbol |= Meaning |
  | ..> | Dependency |
  | ==> | Service Provision |
  | -.-> | Registration |
endlegend

@enduml

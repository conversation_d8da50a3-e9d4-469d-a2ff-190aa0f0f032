/*
 *  Copyright (c) 2023 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Bayerische Motoren Werke Aktiengesellschaft (BMW AG) - initial API and implementation
 *
 */

package org.eclipse.edc.sql.statement;

/**
 * Provide base SQL functionalities.
 */
public interface SqlStatements {

    /**
     * Operator to format an incoming string as JSON. Should return an empty string if the database does not support
     * this. By default, it's the one used by H2, but every different implementation could then override this method
     * with the correct operator.
     *
     * @return JSON cast operator
     */
    default String getFormatAsJsonOperator() {
        return " FORMAT JSON";
    }

    /**
     * Instantiate a new execute statement using the correct JSON operator.
     *
     * @return the {@link SqlExecuteStatement}.
     */
    default SqlExecuteStatement executeStatement() {
        return SqlExecuteStatement.newInstance(getFormatAsJsonOperator());
    }
}

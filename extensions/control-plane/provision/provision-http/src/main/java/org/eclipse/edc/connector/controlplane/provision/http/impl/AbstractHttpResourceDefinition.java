/*
 *  Copyright (c) 2022 Microsoft Corporation
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Microsoft Corporation - initial API and implementation
 *
 */

package org.eclipse.edc.connector.controlplane.provision.http.impl;

import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import org.eclipse.edc.connector.controlplane.transfer.spi.types.ResourceDefinition;

import java.util.Objects;

/**
 * Defines common attributes for HTTP-based provisioning asset resources.
 */
public abstract class AbstractHttpResourceDefinition extends ResourceDefinition {
    protected String dataAddressType;

    protected AbstractHttpResourceDefinition() {
    }

    /**
     * Returns the data address type the definition is associated with. This is used to determine which provisioner will be engaged.
     */
    public String getDataAddressType() {
        return dataAddressType;
    }

    @Override
    public String getTransferProcessId() {
        return transferProcessId;
    }

    protected <RD extends AbstractHttpResourceDefinition, B extends AbstractHttpResourceDefinition.Builder<RD, B>> B initializeBuilder(B builder) {
        return super.initializeBuilder(builder)
                .dataAddressType(dataAddressType);
    }

    @JsonPOJOBuilder(withPrefix = "")
    public static class Builder<RD extends AbstractHttpResourceDefinition, B extends ResourceDefinition.Builder<RD, B>> extends ResourceDefinition.Builder<RD, B> {

        protected Builder(RD definition) {
            super(definition);
        }

        @SuppressWarnings("unchecked")
        public B dataAddressType(String dataAddressType) {
            resourceDefinition.dataAddressType = dataAddressType;
            return (B) this;
        }

        @Override
        protected void verify() {
            super.verify();
            Objects.requireNonNull(resourceDefinition.dataAddressType, "dataAddressType");
        }

    }

}

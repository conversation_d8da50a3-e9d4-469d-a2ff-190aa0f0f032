/*
 *  Copyright (c) 2023 Bayerische Motoren Werke Aktiengesellschaft (BMW AG)
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Bayerische Motoren Werke Aktiengesellschaft (BMW AG) - initial API and implementation
 *
 */

package org.eclipse.edc.connector.policy.monitor.store.sql.schema;

import org.eclipse.edc.connector.policy.monitor.spi.PolicyMonitorEntryStates;
import org.eclipse.edc.sql.lease.StatefulEntityMapping;

/**
 * Maps fields of a {@link org.eclipse.edc.connector.policy.monitor.spi.PolicyMonitorEntry} onto the
 * corresponding SQL schema (= column names) enabling access through Postgres JSON operators where applicable
 */
public class PolicyMonitorMapping extends StatefulEntityMapping {

    public PolicyMonitorMapping(PolicyMonitorStatements statements) {
        super(statements, state -> PolicyMonitorEntryStates.valueOf(state).code());
        add("contractId", statements.getContractIdColumn());
    }

}

# Minimal EDC Connector Dockerfile
FROM openjdk:17-jdk-slim

# Set working directory
WORKDIR /app

# Copy the built JAR file
COPY build/libs/minimal-connector.jar app.jar

# Expose the default ports
EXPOSE 8080 8181 9191

# Set default JVM options
ENV JVM_ARGS="-Xmx1g -Xms512m"

# Use "exec" for Kubernetes graceful termination (SIGINT) to reach JVM
ENTRYPOINT [ "sh", "-c", \
    "exec java $JVM_ARGS -jar app.jar"]

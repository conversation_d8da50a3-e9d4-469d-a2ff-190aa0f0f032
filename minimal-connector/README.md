# Minimal EDC Connector

This is a minimal, runnable Eclipse Dataspace Connector (EDC) that includes only the essential components needed to start an EDC runtime.

## What's Included

This minimal connector comes in two flavors:

### 1. Ultra-Minimal Connector (default)
**Core Components:**
- **Boot System**: Runtime initialization and extension loading
- **Runtime Core**: HTTP client, type management, and core utilities

**Essential Extensions:**
- **Configuration Filesystem**: Loads configuration from properties files
- **HTTP**: Basic HTTP client capabilities
- **API Observability**: Health checks and monitoring endpoints only
- **IAM Mock**: Simple mock authentication (no real security)

**Custom Extensions:**
- **MinimalConnectorExtension**: Example of how to add custom functionality
- **BootSequenceLogger**: Shows the extension lifecycle phases

**What's NOT included:**
- No JSON-LD support (to avoid transitive dependencies)
- No version API (to avoid control plane dependencies)
- No management APIs (to avoid control plane service requirements)

### 2. With Control Plane (optional)
Use `build-with-controlplane.gradle.kts` to include:
- **Control Plane Core**: Contract negotiation, asset management, policy management
- **Management API**: Full REST API for managing the connector
- **SQL Stores**: Persistent storage for assets, contracts, policies, etc.

## Building and Running

### Prerequisites
- Java 17 or higher
- Gradle 7.6 or higher

### Option 1: Ultra-Minimal Connector (Recommended for Learning Boot Sequence)

**Build:**
```bash
./gradlew build
```

**Run with Gradle:**
```bash
./gradlew run
```

**Build and Run Fat JAR:**
```bash
./gradlew shadowJar
java -jar build/libs/minimal-connector.jar
```

### Option 2: With Basic APIs (includes JSON-LD and Version API)

**Build with APIs:**
```bash
./gradlew -b build-with-apis.gradle.kts build
```

**Run with APIs:**
```bash
./gradlew -b build-with-apis.gradle.kts run
```

### Option 3: With Control Plane (Full Connector)

**Build with Control Plane:**
```bash
./gradlew -b build-with-controlplane.gradle.kts build
```

**Run with Control Plane:**
```bash
./gradlew -b build-with-controlplane.gradle.kts run
```

## Available Endpoints

### Ultra-Minimal Connector:
- **Health Check**: http://localhost:8080/api/check/health
- **Readiness Check**: http://localhost:8080/api/check/ready
- **Liveness Check**: http://localhost:8080/api/check/live

### With Control Plane (additional endpoints):
- **Management API**: http://localhost:9191/api/v1/management
- **Assets API**: http://localhost:9191/api/v1/management/v3/assets
- **Policies API**: http://localhost:9191/api/v1/management/v3/policydefinitions
- **Contract Definitions**: http://localhost:9191/api/v1/management/v3/contractdefinitions
- **Contract Negotiations**: http://localhost:9191/api/v1/management/v3/contractnegotiations
- **Transfer Processes**: http://localhost:9191/api/v1/management/v3/transferprocesses

## Configuration

The connector is configured via `src/main/resources/config.properties`. Key settings:

- `edc.participant.id`: Unique identifier for this connector
- `web.http.port`: Main API port (default: 8181)
- `web.http.management.port`: Management API port (default: 9191)
- `web.http.observability.port`: Health check port (default: 8080)

## Security Note

This minimal connector uses basic authentication for demonstration purposes. 
**DO NOT USE IN PRODUCTION** without proper security configuration.

## Adding More Functionality

To add more capabilities to your connector, include additional EDC extensions in the `build.gradle.kts` dependencies:

```kotlin
// For data plane functionality
implementation("org.eclipse.edc:data-plane-core:${edcVersion}")

// For control plane functionality  
implementation("org.eclipse.edc:control-plane-core:${edcVersion}")

// For specific protocols (e.g., DSP)
implementation("org.eclipse.edc:dsp:${edcVersion}")

// For SQL persistence
implementation("org.eclipse.edc:sql-core:${edcVersion}")
```

## Troubleshooting

If you encounter build or runtime issues, check `TROUBLESHOOTING.md` for common solutions.

Common issues:
- **"No default provider for required injection point"** - Missing dependency (usually JSON-LD)
- **Build failures** - Version compatibility or missing dependencies
- **Runtime errors** - Configuration issues or circular dependencies

## Next Steps

1. **Add Data Plane**: Include data plane extensions for actual data transfer
2. **Add Control Plane**: Include control plane for contract negotiation
3. **Add Persistence**: Include SQL or other persistence extensions
4. **Add Security**: Configure proper authentication and authorization
5. **Add Protocols**: Include DSP or other protocol implementations

# Minimal EDC Connector

This is a minimal, runnable Eclipse Dataspace Connector (EDC) that includes only the essential components needed to start an EDC runtime.

## What's Included

### Core Components
- **Boot System**: Runtime initialization and extension loading
- **Connector Core**: Essential EDC services and default implementations
- **Runtime Core**: HTTP client, type management, and core utilities

### Essential Extensions
- **Configuration Filesystem**: Loads configuration from properties files
- **HTTP**: Basic HTTP client capabilities
- **API Core**: REST API infrastructure
- **API Observability**: Health checks and monitoring endpoints
- **Auth Token-based**: Simple token-based authentication
- **Management API**: Basic management endpoints
- **Version API**: Version information endpoint

### Custom Extension
- **MinimalConnectorExtension**: Example of how to add custom functionality

## Building and Running

### Prerequisites
- Java 17 or higher
- Gradle 7.6 or higher

### Build
```bash
./gradlew build
```

### Run with Gradle
```bash
./gradlew run
```

### Build Fat JAR
```bash
./gradlew shadowJar
```

### Run Fat JAR
```bash
java -jar build/libs/minimal-connector.jar
```

## Available Endpoints

Once running, the following endpoints are available:

- **Health Check**: http://localhost:8080/api/check/health
- **Readiness Check**: http://localhost:8080/api/check/ready
- **Liveness Check**: http://localhost:8080/api/check/live
- **Version Info**: http://localhost:8181/api/version
- **Management API**: http://localhost:9191/api/v1/management

## Configuration

The connector is configured via `src/main/resources/config.properties`. Key settings:

- `edc.participant.id`: Unique identifier for this connector
- `web.http.port`: Main API port (default: 8181)
- `web.http.management.port`: Management API port (default: 9191)
- `web.http.observability.port`: Health check port (default: 8080)

## Security Note

This minimal connector uses basic authentication for demonstration purposes. 
**DO NOT USE IN PRODUCTION** without proper security configuration.

## Adding More Functionality

To add more capabilities to your connector, include additional EDC extensions in the `build.gradle.kts` dependencies:

```kotlin
// For data plane functionality
implementation("org.eclipse.edc:data-plane-core:${edcVersion}")

// For control plane functionality  
implementation("org.eclipse.edc:control-plane-core:${edcVersion}")

// For specific protocols (e.g., DSP)
implementation("org.eclipse.edc:dsp:${edcVersion}")

// For SQL persistence
implementation("org.eclipse.edc:sql-core:${edcVersion}")
```

## Troubleshooting

If you encounter build or runtime issues, check `TROUBLESHOOTING.md` for common solutions.

Common issues:
- **"No default provider for required injection point"** - Missing dependency (usually JSON-LD)
- **Build failures** - Version compatibility or missing dependencies
- **Runtime errors** - Configuration issues or circular dependencies

## Next Steps

1. **Add Data Plane**: Include data plane extensions for actual data transfer
2. **Add Control Plane**: Include control plane for contract negotiation
3. **Add Persistence**: Include SQL or other persistence extensions
4. **Add Security**: Configure proper authentication and authorization
5. **Add Protocols**: Include DSP or other protocol implementations

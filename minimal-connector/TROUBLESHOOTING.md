# Troubleshooting EDC Connector Issues

This guide helps you resolve common issues when building and running the minimal EDC connector.

## Common Build/Runtime Issues

### 1. "No default provider for required injection point"

**Error Examples:**
```
No default provider for required injection point Field "jsonLd" of type org.eclipse.edc.jsonld.spi.JsonLd
No default provider for required injection point Field "identityService" of type org.eclipse.edc.spi.iam.IdentityService
No default provider for required injection point Field "edrStore" of type org.eclipse.edc.edr.spi.store.EndpointDataReferenceStore
```

**Cause:** Missing dependency that provides the required service, or including API extensions without their underlying services.

**Solutions:**
```kotlin
// For JSON-LD issues
implementation("org.eclipse.edc:json-ld:${edcVersion}")

// For IdentityService issues
implementation("org.eclipse.edc:iam-mock:${edcVersion}")

// For EDR store issues
implementation("org.eclipse.edc:edr-store-core:${edcVersion}")

// For control plane service issues - either add control plane core or exclude the APIs
implementation("org.eclipse.edc:control-plane-core:${edcVersion}")
```

**Alternative:** Remove `connector-core` and build up dependencies manually to avoid transitive dependencies.

### 2. "Extension not found" or ClassNotFoundException

**Cause:** Extension class not on classpath or not registered.

**Solution:**
1. Check `META-INF/services/org.eclipse.edc.spi.system.ServiceExtension`
2. Verify the extension dependency is in `build.gradle.kts`
3. Ensure the extension class exists and is properly annotated

### 3. Circular Dependency Issues

**Error Example:**
```
Circular dependency detected: ExtensionA -> ExtensionB -> ExtensionA
```

**Solution:**
1. Review `@Inject` annotations in your extensions
2. Make some dependencies optional with `@Inject(required = false)`
3. Use `@Provider` methods to break circular dependencies

### 4. Configuration Issues

**Error Example:**
```
Required setting 'some.property' was not provided
```

**Solution:**
1. Add the property to `config.properties`
2. Set the property as environment variable
3. Make the setting optional in the extension

## Debugging Steps

### 1. Enable Debug Logging

Add to `config.properties`:
```properties
edc.log.level=DEBUG
edc.boot.extension.debug=true
edc.boot.injection.debug=true
```

### 2. Check Dependency Graph

Run with our custom runtime to see extension loading order:
```bash
./gradlew run
```

Look for the "DEPENDENCY GRAPH ANALYSIS" section in the output.

### 3. Verify Extension Registration

Check that your extension is listed in:
```
src/main/resources/META-INF/services/org.eclipse.edc.spi.system.ServiceExtension
```

### 4. Test Minimal Configuration

Start with the absolute minimum and add extensions one by one:

```kotlin
dependencies {
    implementation("org.eclipse.edc:boot:${edcVersion}")
    implementation("org.eclipse.edc:connector-core:${edcVersion}")
    implementation("org.eclipse.edc:runtime-core:${edcVersion}")
    // Add others one by one
}
```

## Common Missing Dependencies

If you encounter injection errors, try adding these common dependencies:

```kotlin
// JSON-LD support (required by many API extensions)
implementation("org.eclipse.edc:json-ld:${edcVersion}")

// Transform support (required by many extensions)
implementation("org.eclipse.edc:transform-core:${edcVersion}")

// Validation support
implementation("org.eclipse.edc:validator-core:${edcVersion}")

// Web/HTTP support
implementation("org.eclipse.edc:jetty-core:${edcVersion}")

// Authentication support
implementation("org.eclipse.edc:auth-configuration:${edcVersion}")
```

## Version Compatibility

Make sure all EDC dependencies use the same version:

```kotlin
val edcVersion = "0.12.0" // Use consistent version
```

Check the [EDC releases](https://github.com/eclipse-edc/Connector/releases) for the latest stable version.

## IDE-Specific Issues

### IntelliJ IDEA
1. Refresh Gradle project
2. Invalidate caches and restart
3. Check that annotation processing is enabled

### Eclipse
1. Refresh and clean project
2. Check build path includes all dependencies

### VS Code
1. Reload Java projects
2. Check Java extension pack is installed

## Getting Help

1. **Check the logs** - Enable DEBUG logging to see detailed error information
2. **Review the dependency graph** - Use our custom runtime to see extension loading order
3. **Start minimal** - Begin with core dependencies and add incrementally
4. **Check EDC documentation** - Visit [EDC documentation](https://eclipse-edc.github.io/docs/)
5. **Community support** - Ask questions in [EDC discussions](https://github.com/eclipse-edc/Connector/discussions)

## Quick Test Script

Use this script to verify your setup:

```bash
#!/bin/bash
echo "Testing EDC Connector..."

# Build
./gradlew clean build
if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

# Quick start test
timeout 10s ./gradlew run > test.log 2>&1 &
sleep 5

if grep -q "ready" test.log; then
    echo "✅ Connector starts successfully"
else
    echo "❌ Connector failed to start"
    echo "Last 20 lines of log:"
    tail -20 test.log
fi

rm -f test.log
```

This troubleshooting guide should help you resolve most common issues with the minimal EDC connector!

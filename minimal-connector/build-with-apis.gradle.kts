/*
 *  Copyright (c) 2024 Eclipse Dataspace Connector
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Eclipse Dataspace Connector - Minimal Connector with APIs
 *
 */

plugins {
    `java-library`
    id("application")
    id("com.github.johnrengelman.shadow") version "8.1.1"
}

val edcVersion: String by project
val javaVersion: String by project

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(javaVersion))
    }
}

dependencies {
    // Core EDC dependencies
    implementation("org.eclipse.edc:boot:${edcVersion}")
    implementation("org.eclipse.edc:runtime-core:${edcVersion}")
    
    // JSON-LD support (required for APIs)
    implementation("org.eclipse.edc:json-ld:${edcVersion}")
    
    // Essential extensions
    implementation("org.eclipse.edc:configuration-filesystem:${edcVersion}")
    implementation("org.eclipse.edc:http:${edcVersion}")
    
    // API support
    implementation("org.eclipse.edc:api-core:${edcVersion}")
    implementation("org.eclipse.edc:api-observability:${edcVersion}")
    implementation("org.eclipse.edc:version-api:${edcVersion}")
    
    // Authentication
    implementation("org.eclipse.edc:iam-mock:${edcVersion}")
}

application {
    mainClass.set("org.example.connector.CustomRuntime")
}

tasks.withType<com.github.johnrengelman.gradle.plugins.shadow.tasks.ShadowJar> {
    exclude("**/pom.properties", "**/pom.xml")
    mergeServiceFiles()
    archiveFileName.set("minimal-connector-with-apis.jar")
}

repositories {
    mavenCentral()
    maven {
        url = uri("https://central.sonatype.com/repository/maven-snapshots/")
    }
}

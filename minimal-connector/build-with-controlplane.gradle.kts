/*
 *  Copyright (c) 2024 Eclipse Dataspace Connector
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Eclipse Dataspace Connector - Minimal Connector with Control Plane
 *
 */

plugins {
    `java-library`
    id("application")
    id("com.github.johnrengelman.shadow") version "8.1.1"
}

val edcVersion: String by project
val javaVersion: String by project

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(javaVersion))
    }
}

dependencies {
    // Core EDC dependencies
    implementation("org.eclipse.edc:boot:${edcVersion}")
    implementation("org.eclipse.edc:connector-core:${edcVersion}")
    implementation("org.eclipse.edc:runtime-core:${edcVersion}")
    
    // JSON-LD support
    implementation("org.eclipse.edc:json-ld:${edcVersion}")
    
    // Configuration and HTTP
    implementation("org.eclipse.edc:configuration-filesystem:${edcVersion}")
    implementation("org.eclipse.edc:http:${edcVersion}")
    
    // Control Plane Core (provides the services that APIs need)
    implementation("org.eclipse.edc:control-plane-core:${edcVersion}")
    
    // API support
    implementation("org.eclipse.edc:api-core:${edcVersion}")
    implementation("org.eclipse.edc:api-observability:${edcVersion}")
    implementation("org.eclipse.edc:version-api:${edcVersion}")
    implementation("org.eclipse.edc:management-api:${edcVersion}")
    
    // Authentication
    implementation("org.eclipse.edc:auth-tokenbased:${edcVersion}")
    
    // In-memory stores (for development - replace with SQL for production)
    implementation("org.eclipse.edc:asset-index-sql:${edcVersion}")
    implementation("org.eclipse.edc:contract-definition-store-sql:${edcVersion}")
    implementation("org.eclipse.edc:contract-negotiation-store-sql:${edcVersion}")
    implementation("org.eclipse.edc:policy-definition-store-sql:${edcVersion}")
    implementation("org.eclipse.edc:transfer-process-store-sql:${edcVersion}")
    
    // Transaction support
    implementation("org.eclipse.edc:transaction-local:${edcVersion}")
}

application {
    mainClass.set("org.example.connector.CustomRuntime")
}

tasks.withType<com.github.johnrengelman.gradle.plugins.shadow.tasks.ShadowJar> {
    exclude("**/pom.properties", "**/pom.xml")
    mergeServiceFiles()
    archiveFileName.set("minimal-connector-with-controlplane.jar")
}

repositories {
    mavenCentral()
    maven {
        url = uri("https://central.sonatype.com/repository/maven-snapshots/")
    }
}

/*
 *  Copyright (c) 2024 Eclipse Dataspace Connector
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Eclipse Dataspace Connector - Minimal Connector Example
 *
 */

plugins {
    `java-library`
    id("application")
    id("com.github.johnrengelman.shadow") version "8.1.1"
}

val edcVersion: String by project
val javaVersion: String by project

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(javaVersion))
    }
}

dependencies {
    // Core EDC dependencies - absolute minimum required
    implementation("org.eclipse.edc:boot:${edcVersion}")
    implementation("org.eclipse.edc:runtime-core:${edcVersion}")

    // Essential extensions for a working connector
    implementation("org.eclipse.edc:configuration-filesystem:${edcVersion}")
    implementation("org.eclipse.edc:http:${edcVersion}")

    // Web server support (required for API endpoints)
    implementation("org.eclipse.edc:jetty-core:${edcVersion}")

    // Basic API support - only observability (health checks)
    implementation("org.eclipse.edc:api-observability:${edcVersion}")

    // Authentication support - use mock for minimal setup
    implementation("org.eclipse.edc:iam-mock:${edcVersion}")
}

application {
    // Option 1: Custom runtime with detailed boot sequence logging
    mainClass.set("org.example.connector.CustomRuntime")

    // Option 2: Simple approach with standard runtime (uncomment to use)
    // mainClass.set("org.example.connector.SimpleBootLogger")

    // Option 3: Standard EDC runtime (uncomment to use)
    // mainClass.set("org.eclipse.edc.boot.system.runtime.BaseRuntime")
}

tasks.withType<com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar> {
    exclude("**/pom.properties", "**/pom.xml")
    mergeServiceFiles()
    archiveFileName.set("minimal-connector.jar")
}

repositories {
    mavenCentral()
    maven {
        url = uri("https://central.sonatype.com/repository/maven-snapshots/")
    }
}

/*
 *  Copyright (c) 2024 Eclipse Dataspace Connector
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Eclipse Dataspace Connector - Minimal Connector Example
 *
 */

plugins {
    `java-library`
    id("application")
    id("com.github.johnrengelman.shadow") version "8.1.1"
}

val edcVersion: String by project
val javaVersion: String by project

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(javaVersion))
    }
}

dependencies {
    // Core EDC dependencies - absolute minimum required
    implementation("org.eclipse.edc:boot:${edcVersion}")
    implementation("org.eclipse.edc:connector-core:${edcVersion}")
    implementation("org.eclipse.edc:runtime-core:${edcVersion}")
    
    // Essential extensions for a working connector
    implementation("org.eclipse.edc:configuration-filesystem:${edcVersion}")
    implementation("org.eclipse.edc:http:${edcVersion}")
    implementation("org.eclipse.edc:api-core:${edcVersion}")
    implementation("org.eclipse.edc:api-observability:${edcVersion}")
    
    // Optional but recommended for basic functionality
    implementation("org.eclipse.edc:auth-tokenbased:${edcVersion}")
    implementation("org.eclipse.edc:management-api:${edcVersion}")
    implementation("org.eclipse.edc:version-api:${edcVersion}")
}

application {
    mainClass.set("org.eclipse.edc.boot.system.runtime.BaseRuntime")
}

tasks.withType<com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar> {
    exclude("**/pom.properties", "**/pom.xml")
    mergeServiceFiles()
    archiveFileName.set("minimal-connector.jar")
}

repositories {
    mavenCentral()
    maven {
        url = uri("https://central.sonatype.com/repository/maven-snapshots/")
    }
}

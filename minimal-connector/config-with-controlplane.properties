# Minimal EDC Connector Configuration with Control Plane

# Basic connector settings
edc.participant.id=minimal-connector
edc.hostname=localhost

# Web server configuration
web.http.port=8181
web.http.path=/api

# Management API configuration  
web.http.management.port=9191
web.http.management.path=/api/v1/management

# Health check endpoint
web.http.observability.port=8080
web.http.observability.path=/api/check

# Logging level - set to DEBUG to see detailed boot sequence
edc.log.level=DEBUG

# Enable detailed extension loading logs
edc.boot.extension.debug=true

# Show dependency injection details
edc.boot.injection.debug=true

# Disable authentication for this minimal example (NOT for production!)
edc.api.auth.key=password

# Database configuration (using H2 in-memory for simplicity)
edc.datasource.default.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1
edc.datasource.default.user=sa
edc.datasource.default.password=

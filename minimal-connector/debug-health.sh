#!/bin/bash

# Debug script to understand why health endpoints return 503

echo "=== Debugging Health Endpoints ==="
echo ""

# Build first
echo "Building..."
./gradlew clean build -q

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"

# Start connector in background
echo "Starting connector with debug logging..."
./gradlew run > debug-health.log 2>&1 &
CONNECTOR_PID=$!

# Wait for startup
echo "Waiting for startup (12 seconds)..."
sleep 12

echo ""
echo "=== Testing Endpoints ==="

# Function to test endpoint with full response
test_endpoint_detailed() {
    local name="$1"
    local url="$2"
    
    echo ""
    echo "Testing $name:"
    echo "  URL: $url"
    
    # Get full response
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url" 2>/dev/null)
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    body=$(echo "$response" | sed 's/HTTPSTATUS:[0-9]*$//')
    
    echo "  Status: $http_code"
    if [ -n "$body" ] && [ "$body" != "null" ]; then
        echo "  Response Body: $body"
    fi
}

test_endpoint_detailed "Health Check" "http://localhost:8080/api/check/health"
test_endpoint_detailed "Readiness Check" "http://localhost:8080/api/check/readiness"
test_endpoint_detailed "Liveness Check" "http://localhost:8080/api/check/liveness"
test_endpoint_detailed "Startup Check" "http://localhost:8080/api/check/startup"

# Stop connector
echo ""
echo "Stopping connector..."
kill $CONNECTOR_PID 2>/dev/null
wait $CONNECTOR_PID 2>/dev/null

echo ""
echo "=== Log Analysis ==="

# Check if BasicHealthProvider was loaded
echo ""
echo "1. Checking if BasicHealthProvider was loaded:"
if grep -q "BasicHealthProvider" debug-health.log; then
    echo "✅ BasicHealthProvider found in logs"
    grep "BasicHealthProvider" debug-health.log | head -5
else
    echo "❌ BasicHealthProvider NOT found in logs"
fi

# Check for health service registration
echo ""
echo "2. Checking health provider registration:"
if grep -q "health provider.*registered" debug-health.log; then
    echo "✅ Health providers were registered"
    grep "health provider.*registered" debug-health.log
else
    echo "❌ No health provider registration found"
fi

# Check for ObservabilityApiExtension
echo ""
echo "3. Checking ObservabilityApiExtension:"
if grep -q "Observability" debug-health.log; then
    echo "✅ ObservabilityApiExtension found"
    grep "Observability" debug-health.log | head -3
else
    echo "❌ ObservabilityApiExtension NOT found"
fi

# Check for any health-related errors
echo ""
echo "4. Checking for health-related errors:"
if grep -qi "health.*error\|health.*fail" debug-health.log; then
    echo "❌ Health-related errors found:"
    grep -i "health.*error\|health.*fail" debug-health.log
else
    echo "✅ No health-related errors found"
fi

# Check extension loading order
echo ""
echo "5. Extension loading order:"
if grep -q "Extension loading order" debug-health.log; then
    echo "✅ Extension loading order found:"
    grep -A 20 "Extension loading order" debug-health.log | head -15
else
    echo "⚠️  Extension loading order not found in logs"
fi

echo ""
echo "=== Debug Complete ==="
echo ""
echo "If health endpoints still return 503, check the log analysis above."
echo "The issue might be:"
echo "• BasicHealthProvider not being loaded"
echo "• Health providers not being registered properly"
echo "• ObservabilityApiExtension not working correctly"

# Keep log for manual inspection
echo ""
echo "Full log saved as: debug-health.log"

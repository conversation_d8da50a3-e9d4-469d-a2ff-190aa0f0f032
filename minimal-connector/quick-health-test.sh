#!/bin/bash

# Quick test to verify health endpoints work

echo "=== Quick Health Test ==="
echo ""

# Build first
echo "Building..."
./gradlew clean build -q

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"

# Start connector in background
echo "Starting connector..."
./gradlew run > health-test.log 2>&1 &
CONNECTOR_PID=$!

# Wait for startup
echo "Waiting for startup (10 seconds)..."
sleep 10

# Test health endpoints
echo ""
echo "Testing health endpoints..."

# Function to test endpoint
test_endpoint() {
    local name="$1"
    local url="$2"
    
    echo -n "  $name: "
    response=$(curl -s -w "%{http_code}" "$url" 2>/dev/null)
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        echo "✅ $http_code OK"
    elif [ "$http_code" = "503" ]; then
        echo "⚠️  $http_code Service Unavailable (may be normal)"
    else
        echo "❌ $http_code"
    fi
}

test_endpoint "Health" "http://localhost:8080/api/check/health"
test_endpoint "Readiness" "http://localhost:8080/api/check/readiness"
test_endpoint "Liveness" "http://localhost:8080/api/check/liveness"
test_endpoint "Startup" "http://localhost:8080/api/check/startup"

# Stop connector
echo ""
echo "Stopping connector..."
kill $CONNECTOR_PID 2>/dev/null
wait $CONNECTOR_PID 2>/dev/null

# Check for errors in log
echo ""
echo "Checking for errors..."
if grep -q "ERROR\|SEVERE\|missing" health-test.log; then
    echo "❌ Errors found in log:"
    grep "ERROR\|SEVERE\|missing" health-test.log | head -5
else
    echo "✅ No errors found in log"
fi

# Check if health provider was registered
if grep -q "Basic health provider registered" health-test.log; then
    echo "✅ Basic health provider was registered"
else
    echo "⚠️  Basic health provider registration not found in log"
fi

echo ""
echo "=== Test Complete ==="

# Cleanup
rm -f health-test.log

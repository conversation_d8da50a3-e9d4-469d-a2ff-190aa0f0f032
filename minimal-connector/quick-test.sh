#!/bin/bash

# Quick test to verify the minimal connector builds and starts without dependency injection errors

echo "=== Quick Test: Minimal EDC Connector ==="
echo ""

# Clean and build
echo "1. Building..."
./gradlew clean build -q

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"

# Test startup for 8 seconds
echo "2. Testing startup (8 seconds)..."
timeout 8s ./gradlew run > quick-test.log 2>&1 &
TEST_PID=$!

# Wait for startup
sleep 5

# Check if still running
if kill -0 $TEST_PID 2>/dev/null; then
    echo "✅ Connector started successfully"
    kill $TEST_PID 2>/dev/null
    wait $TEST_PID 2>/dev/null
else
    echo "❌ Connector failed to start"
fi

# Check for dependency injection errors
echo "3. Checking for dependency injection errors..."

if grep -q "missing" quick-test.log; then
    echo "❌ Dependency injection errors found:"
    grep "missing" quick-test.log | head -5
    echo ""
    echo "See TROUBLESHOOTING.md for solutions"
    exit 1
else
    echo "✅ No dependency injection errors found"
fi

# Check for successful boot
if grep -q "ready\|BOOT SEQUENCE COMPLETED" quick-test.log; then
    echo "✅ Connector completed startup successfully"
else
    echo "⚠️  Connector startup may not have completed"
    echo "Last 5 lines of log:"
    tail -5 quick-test.log
fi

echo ""
echo "=== Test Complete ==="
echo ""
echo "Available endpoints:"
echo "• Health: http://localhost:8080/api/check/health"
echo "• Version: http://localhost:8181/api/version"

# Cleanup
rm -f quick-test.log

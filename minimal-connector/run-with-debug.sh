#!/bin/bash

# Script to run the connector with maximum debugging visibility

echo "=== Running Minimal EDC Connector with Full Debug Output ==="
echo ""

# Build first
echo "Building connector..."
./gradlew build -q

echo ""
echo "Starting connector with debug output..."
echo "Press Ctrl+C to stop"
echo ""

# Run with detailed JVM and EDC debugging
java -Djava.util.logging.ConsoleHandler.level=ALL \
     -Djava.util.logging.level=ALL \
     -Dedc.log.level=DEBUG \
     -Dedc.boot.extension.debug=true \
     -Dedc.boot.injection.debug=true \
     -Dedc.fs.config=src/main/resources/config.properties \
     -Xmx1g \
     -Xms512m \
     -jar build/libs/minimal-connector.jar

/*
 *  Copyright (c) 2024 Eclipse Dataspace Connector
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Eclipse Dataspace Connector - Basic Health Provider
 *
 */

package org.example.connector;

import org.eclipse.edc.runtime.metamodel.annotation.Extension;
import org.eclipse.edc.runtime.metamodel.annotation.Inject;
import org.eclipse.edc.spi.system.ServiceExtension;
import org.eclipse.edc.spi.system.ServiceExtensionContext;
import org.eclipse.edc.spi.system.health.HealthCheckResult;
import org.eclipse.edc.spi.system.health.HealthCheckService;

/**
 * Extension that provides basic health checks to ensure the health endpoints
 * return 200 OK instead of 503 Service Unavailable.
 */
@Extension(value = BasicHealthProvider.NAME)
public class BasicHealthProvider implements ServiceExtension {

    public static final String NAME = "Basic Health Provider";

    @Inject
    private HealthCheckService healthCheckService;

    @Override
    public String name() {
        return NAME;
    }

    @Override
    public void initialize(ServiceExtensionContext context) {
        var monitor = context.getMonitor();
        
        // Create a simple health check that always returns success
        var healthCheck = () -> HealthCheckResult.Builder.newInstance()
                .component("MinimalConnector")
                .success()
                .build();

        // Register this health check for all health aspects
        healthCheckService.addReadinessProvider(healthCheck);
        healthCheckService.addLivenessProvider(healthCheck);
        healthCheckService.addStartupStatusProvider(healthCheck);
        
        monitor.info("Basic health provider registered - health endpoints will return 200 OK");
    }
}

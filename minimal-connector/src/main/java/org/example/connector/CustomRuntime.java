/*
 *  Copyright (c) 2024 Eclipse Dataspace Connector
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Eclipse Dataspace Connector - Custom Runtime for Boot Sequence Visibility
 *
 */

package org.example.connector;

import org.eclipse.edc.boot.system.DependencyGraph;
import org.eclipse.edc.boot.system.injection.lifecycle.ExtensionLifecycleManager;
import org.eclipse.edc.boot.system.runtime.BaseRuntime;
import org.eclipse.edc.spi.system.ServiceExtension;
import org.eclipse.edc.spi.system.ServiceExtensionContext;

import java.util.List;

/**
 * Custom runtime that provides detailed logging of the boot sequence.
 * This extends BaseRuntime to add visibility into what happens during startup.
 */
public class CustomRuntime extends BaseRuntime {

    public static void main(String[] args) {
        System.out.println("=== STARTING CUSTOM EDC RUNTIME ===");
        System.out.println("This runtime will show you exactly what happens during boot...");

        var runtime = new CustomRuntime();
        runtime.boot(true);
    }

    @Override
    public void boot(boolean addShutdownHook) {
        System.out.println("\n=== BOOT SEQUENCE STARTED ===");

        // Step 1: Create Monitor
        System.out.println("Step 1: Creating Monitor...");
        monitor = createMonitor();
        monitor.info("Monitor created successfully");

        // Step 2: Load Configuration
        System.out.println("Step 2: Loading Configuration...");
        var config = configurationLoader.loadConfiguration(monitor);
        monitor.info("Configuration loaded from properties files");

        // Step 3: Create Service Extension Context
        System.out.println("Step 3: Creating Service Extension Context...");
        context = createServiceExtensionContext(config);
        monitor.info("Service Extension Context created and initialized");

        try {
            // Step 4: Build Dependency Graph
            System.out.println("Step 4: Building Dependency Graph...");
            var graph = buildDependencyGraph(context);
            logDependencyGraph(graph);

            if (!graph.isValid()) {
                monitor.severe("Dependency graph is invalid!");
                graph.getProblems().forEach(monitor::severe);
                return;
            }

            // Step 5: Boot Extensions
            System.out.println("Step 5: Booting Extensions...");
            bootExtensions(context, graph);

            serviceExtensions = graph.getExtensions();
            monitor.info("All extensions booted successfully. Total: " + serviceExtensions.size());

            // Step 6: Setup Shutdown Hook
            if (addShutdownHook) {
                System.out.println("Step 6: Setting up shutdown hook...");
                getRuntime().addShutdownHook(new Thread(this::shutdown));
            }

            System.out.println("\n=== BOOT SEQUENCE COMPLETED SUCCESSFULLY ===");
            monitor.info("Runtime " + context.getRuntimeId() + " is ready!");

        } catch (Throwable e) {
            System.err.println("=== BOOT SEQUENCE FAILED ===");
            onError(e);
        }
    }

    @Override
    protected void bootExtensions(ServiceExtensionContext context, DependencyGraph graph) {
        monitor.info("Starting extension lifecycle management...");

        var containers = graph.getInjectionContainers();
        monitor.info("Found " + containers.size() + " extensions to boot");

        // Log each extension before booting
        for (var container : containers) {
            var extension = container.getInjectionTarget();
            monitor.info("Booting extension: " + extension.name());
        }

        ExtensionLifecycleManager.bootServiceExtensions(containers, context);
        monitor.info("All extensions have completed their lifecycle");
    }

    private void logDependencyGraph(DependencyGraph graph) {
        monitor.info("=== DEPENDENCY GRAPH ANALYSIS ===");

        List<ServiceExtension> extensions = graph.getExtensions();
        monitor.info("Total extensions found: " + extensions.size());

        monitor.info("Extension loading order:");
        for (int i = 0; i < extensions.size(); i++) {
            var extension = extensions.get(i);
            monitor.info("  " + (i + 1) + ". " + extension.name() + " (" + extension.getClass().getSimpleName() + ")");
        }

        if (!graph.isValid()) {
            monitor.warning("Dependency graph has problems:");
            graph.getProblems().forEach(problem -> monitor.warning("  - " + problem));
        } else {
            monitor.info("Dependency graph is valid ✓");
        }

        monitor.info("=== END DEPENDENCY GRAPH ANALYSIS ===");
    }

    @Override
    public void shutdown() {
        monitor.info("=== SHUTDOWN SEQUENCE STARTED ===");
        super.shutdown();
        monitor.info("=== SHUTDOWN SEQUENCE COMPLETED ===");
    }
}

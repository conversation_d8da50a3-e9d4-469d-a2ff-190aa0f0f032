/*
 *  Copyright (c) 2024 Eclipse Dataspace Connector
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Eclipse Dataspace Connector - Minimal Connector Example
 *
 */

package org.example.connector;

import org.eclipse.edc.runtime.metamodel.annotation.Extension;
import org.eclipse.edc.spi.system.ServiceExtension;
import org.eclipse.edc.spi.system.ServiceExtensionContext;

/**
 * Minimal custom extension to demonstrate how to add custom functionality
 * to an EDC connector.
 */
@Extension(value = MinimalConnectorExtension.NAME)
public class MinimalConnectorExtension implements ServiceExtension {

    public static final String NAME = "Minimal Connector Extension";

    @Override
    public String name() {
        return NAME;
    }

    @Override
    public void initialize(ServiceExtensionContext context) {
        var monitor = context.getMonitor();
        monitor.info("Minimal Connector Extension initialized successfully!");
        monitor.info("Participant ID: " + context.getParticipantId());
        monitor.info("Runtime ID: " + context.getRuntimeId());
    }

    @Override
    public void start() {
        // Add any startup logic here
    }

    @Override
    public void shutdown() {
        // Add any cleanup logic here
    }
}

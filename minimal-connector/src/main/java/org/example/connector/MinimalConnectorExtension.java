/*
 *  Copyright (c) 2024 Eclipse Dataspace Connector
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Eclipse Dataspace Connector - Minimal Connector Example
 *
 */

package org.example.connector;

import org.eclipse.edc.runtime.metamodel.annotation.Extension;
import org.eclipse.edc.runtime.metamodel.annotation.Inject;
import org.eclipse.edc.spi.system.ServiceExtension;
import org.eclipse.edc.spi.system.ServiceExtensionContext;
import org.eclipse.edc.spi.system.health.HealthCheckResult;
import org.eclipse.edc.spi.system.health.HealthCheckService;

/**
 * Minimal custom extension to demonstrate how to add custom functionality
 * to an EDC connector.
 */
@Extension(value = MinimalConnectorExtension.NAME)
public class MinimalConnectorExtension implements ServiceExtension {

    public static final String NAME = "Minimal Connector Extension";

    @Inject
    private HealthCheckService healthCheckService;

    @Override
    public String name() {
        return NAME;
    }

    @Override
    public void initialize(ServiceExtensionContext context) {
        var monitor = context.getMonitor();
        monitor.info("Minimal Connector Extension initialized successfully!");
        monitor.info("Participant ID: " + context.getParticipantId());
        monitor.info("Runtime ID: " + context.getRuntimeId());

        // Register a simple health check that always returns success
        var healthCheck = () -> HealthCheckResult.Builder.newInstance()
                .component("MinimalConnector")
                .success()
                .build();

        // Register this health check for all health aspects
        healthCheckService.addReadinessProvider(healthCheck);
        healthCheckService.addLivenessProvider(healthCheck);
        healthCheckService.addStartupStatusProvider(healthCheck);

        monitor.info("Health providers registered - endpoints should return 200 OK");
    }

    @Override
    public void start() {
        // Add any startup logic here
    }

    @Override
    public void shutdown() {
        // Add any cleanup logic here
    }
}

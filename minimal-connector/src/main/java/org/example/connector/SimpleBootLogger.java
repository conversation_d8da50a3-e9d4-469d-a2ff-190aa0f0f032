/*
 *  Copyright (c) 2024 Eclipse Dataspace Connector
 *
 *  This program and the accompanying materials are made available under the
 *  terms of the Apache License, Version 2.0 which is available at
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Contributors:
 *       Eclipse Dataspace Connector - Simple Boot Logger
 *
 */

package org.example.connector;

import org.eclipse.edc.boot.system.runtime.BaseRuntime;

/**
 * Alternative approach: Use the standard BaseRuntime but with enhanced logging
 * configuration to see the boot sequence without custom runtime code.
 */
public class SimpleBootLogger {

    public static void main(String[] args) {
        System.out.println("=== STARTING EDC WITH ENHANCED LOGGING ===");
        System.out.println("Watch the console output to see the boot sequence...");
        System.out.println("");
        
        // Set system properties for maximum visibility
        System.setProperty("edc.log.level", "DEBUG");
        System.setProperty("java.util.logging.ConsoleHandler.level", "ALL");
        
        // Use the standard BaseRuntime
        BaseRuntime.main(args);
    }
}

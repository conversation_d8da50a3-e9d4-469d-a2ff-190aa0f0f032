# Minimal EDC Connector Configuration

# Basic connector settings
edc.participant.id=minimal-connector
edc.hostname=localhost

# Web server configuration (for version and health APIs)
web.http.port=8181
web.http.path=/api

# Health check endpoint
web.http.observability.port=8080
web.http.observability.path=/api/check

# Logging level - set to DEBUG to see detailed boot sequence
edc.log.level=DEBUG

# Enable detailed extension loading logs
edc.boot.extension.debug=true

# Show dependency injection details
edc.boot.injection.debug=true

# Disable authentication for this minimal example (NOT for production!)
edc.api.auth.key=password

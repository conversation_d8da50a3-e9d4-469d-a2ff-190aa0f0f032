# Minimal EDC Connector Configuration

# Basic connector settings
edc.participant.id=minimal-connector
edc.hostname=localhost

# Web server configuration
web.http.port=8181
web.http.path=/api

# Management API configuration  
web.http.management.port=9191
web.http.management.path=/api/v1/management

# Health check endpoint
web.http.observability.port=8080
web.http.observability.path=/api/check

# Logging level
edc.log.level=INFO

# Disable authentication for this minimal example (NOT for production!)
edc.api.auth.key=password

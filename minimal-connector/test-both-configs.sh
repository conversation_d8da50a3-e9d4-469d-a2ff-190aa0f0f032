#!/bin/bash

# Test script for both minimal connector configurations

echo "=== Testing Both Minimal EDC Connector Configurations ==="
echo ""

# Test 1: Basic Minimal Connector
echo "1. Testing Basic Minimal Connector..."
echo "   Building..."
./gradlew clean build -q

if [ $? -ne 0 ]; then
    echo "   ❌ Basic build failed!"
    exit 1
fi

echo "   ✅ Basic build successful!"

# Quick start test for basic connector
echo "   Testing startup (10 seconds)..."
timeout 10s ./gradlew run > basic-test.log 2>&1 &
BASIC_PID=$!
sleep 5

if kill -0 $BASIC_PID 2>/dev/null; then
    echo "   ✅ Basic connector starts successfully"
    kill $BASIC_PID 2>/dev/null
    wait $BASIC_PID 2>/dev/null
else
    echo "   ❌ Basic connector failed to start"
    echo "   Last 10 lines of log:"
    tail -10 basic-test.log
    exit 1
fi

# Check for dependency injection errors
if grep -q "missing" basic-test.log; then
    echo "   ❌ Dependency injection errors found:"
    grep "missing" basic-test.log
    exit 1
else
    echo "   ✅ No dependency injection errors"
fi

echo ""

# Test 2: Control Plane Connector
echo "2. Testing Control Plane Connector..."
echo "   Building..."
./gradlew -b build-with-controlplane.gradle.kts clean build -q

if [ $? -ne 0 ]; then
    echo "   ❌ Control plane build failed!"
    exit 1
fi

echo "   ✅ Control plane build successful!"

# Quick start test for control plane connector
echo "   Testing startup (10 seconds)..."
timeout 10s ./gradlew -b build-with-controlplane.gradle.kts run > controlplane-test.log 2>&1 &
CP_PID=$!
sleep 5

if kill -0 $CP_PID 2>/dev/null; then
    echo "   ✅ Control plane connector starts successfully"
    kill $CP_PID 2>/dev/null
    wait $CP_PID 2>/dev/null
else
    echo "   ❌ Control plane connector failed to start"
    echo "   Last 10 lines of log:"
    tail -10 controlplane-test.log
    exit 1
fi

# Check for dependency injection errors
if grep -q "missing" controlplane-test.log; then
    echo "   ❌ Control plane dependency injection errors found:"
    grep "missing" controlplane-test.log
    exit 1
else
    echo "   ✅ No control plane dependency injection errors"
fi

echo ""
echo "=== All Tests Passed! ==="
echo ""
echo "Both configurations work correctly:"
echo "• Basic Minimal Connector: Health checks and version API only"
echo "• Control Plane Connector: Full management API capabilities"
echo ""
echo "Choose the configuration that fits your needs:"
echo "• Learning/Development: Use basic minimal connector"
echo "• Production/Full Features: Use control plane connector"

# Cleanup
rm -f basic-test.log controlplane-test.log

#!/bin/bash

# Test script to verify the build works correctly

echo "=== Testing Minimal EDC Connector Build ==="
echo ""

# Clean and build
echo "1. Cleaning and building..."
./gradlew clean build

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"
echo ""

# Test shadowJar
echo "2. Building fat JAR..."
./gradlew shadowJar

if [ $? -ne 0 ]; then
    echo "❌ Shadow JAR build failed!"
    exit 1
fi

echo "✅ Shadow JAR build successful!"
echo ""

# Check if JAR exists
if [ -f "build/libs/minimal-connector.jar" ]; then
    echo "✅ JAR file created: build/libs/minimal-connector.jar"
    echo "   Size: $(du -h build/libs/minimal-connector.jar | cut -f1)"
else
    echo "❌ JAR file not found!"
    exit 1
fi

echo ""
echo "3. Testing JAR execution (5 second test)..."

# Start the connector in background
timeout 5s java -jar build/libs/minimal-connector.jar > test-output.log 2>&1 &
CONNECTOR_PID=$!

# Wait a moment for startup
sleep 2

# Check if process is running
if kill -0 $CONNECTOR_PID 2>/dev/null; then
    echo "✅ Connector started successfully"
    
    # Kill the process
    kill $CONNECTOR_PID 2>/dev/null
    wait $CONNECTOR_PID 2>/dev/null
else
    echo "❌ Connector failed to start"
    echo "Output:"
    cat test-output.log
    exit 1
fi

echo ""
echo "4. Checking log output..."

# Check for key boot sequence messages
if grep -q "BOOT SEQUENCE STARTED" test-output.log; then
    echo "✅ Custom boot sequence logging working"
else
    echo "⚠️  Custom boot sequence logging not found (might be using different main class)"
fi

if grep -q "Boot Services" test-output.log; then
    echo "✅ Extensions are loading"
else
    echo "❌ Extensions not loading properly"
    exit 1
fi

if grep -q "ready" test-output.log; then
    echo "✅ Runtime completed startup"
else
    echo "❌ Runtime did not complete startup"
    exit 1
fi

echo ""
echo "=== All Tests Passed! ==="
echo ""
echo "Your minimal EDC connector is working correctly."
echo "You can now run it with: ./gradlew run"
echo "Or with the JAR: java -jar build/libs/minimal-connector.jar"

# Cleanup
rm -f test-output.log

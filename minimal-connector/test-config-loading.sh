#!/bin/bash

# Test to verify configuration file is being loaded

echo "=== Testing Configuration Loading ==="
echo ""

# Build first
echo "Building..."
./gradlew clean build -q

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"

# Start connector
echo "Starting connector to test configuration loading..."
timeout 15s ./gradlew run > config-test.log 2>&1

echo ""
echo "=== Configuration Analysis ==="

# Check if configuration file was found and loaded
echo ""
echo "1. Checking if configuration file was loaded:"
if grep -q "config.properties\|Configuration.*loaded\|Loading configuration" config-test.log; then
    echo "✅ Configuration loading found in logs:"
    grep -i "config.*properties\|configuration.*load" config-test.log | head -3
else
    echo "⚠️  Configuration loading not explicitly mentioned in logs"
fi

# Check if our specific configuration values are being used
echo ""
echo "2. Checking for our configuration values:"

# Check participant ID
if grep -q "minimal-connector" config-test.log; then
    echo "✅ Participant ID 'minimal-connector' found in logs"
else
    echo "❌ Participant ID 'minimal-connector' NOT found - config may not be loaded"
fi

# Check port configuration
if grep -q "8080\|8181" config-test.log; then
    echo "✅ Port configuration (8080/8181) found in logs"
else
    echo "❌ Port configuration NOT found - config may not be loaded"
fi

# Check debug logging level
if grep -q "DEBUG.*level\|Log level.*DEBUG" config-test.log; then
    echo "✅ DEBUG log level found - configuration is working"
else
    echo "⚠️  DEBUG log level not explicitly mentioned"
fi

# Check if web server started on configured ports
echo ""
echo "3. Checking if web server started on configured ports:"
if grep -qi "port.*8080\|8080.*port\|listening.*8080" config-test.log; then
    echo "✅ Web server started on port 8080 (observability)"
else
    echo "❌ Port 8080 not found in startup logs"
fi

if grep -qi "port.*8181\|8181.*port\|listening.*8181" config-test.log; then
    echo "✅ Web server started on port 8181 (main API)"
else
    echo "❌ Port 8181 not found in startup logs"
fi

# Show any configuration-related errors
echo ""
echo "4. Checking for configuration errors:"
if grep -qi "config.*error\|configuration.*fail\|property.*not.*found" config-test.log; then
    echo "❌ Configuration errors found:"
    grep -i "config.*error\|configuration.*fail\|property.*not.*found" config-test.log
else
    echo "✅ No configuration errors found"
fi

echo ""
echo "=== Test Complete ==="
echo ""
echo "If configuration is not being loaded properly:"
echo "• Check that -Dedc.fs.config=src/main/resources/config.properties is set"
echo "• Verify config.properties file exists and is readable"
echo "• Check that configuration-filesystem extension is included"

# Keep log for inspection
echo ""
echo "Full log saved as: config-test.log"

#!/bin/bash

# Test script for the minimal EDC connector

echo "Testing Ultra-Minimal EDC Connector..."

# Wait for connector to start
echo "Waiting for connector to start..."
sleep 10

# Test health endpoint
echo "Testing health endpoint..."
curl -s -w "Status: %{http_code}\n" http://localhost:8080/api/check/health || echo "Health check failed"

# Test readiness endpoint
echo "Testing readiness endpoint..."
curl -s -w "Status: %{http_code}\n" http://localhost:8080/api/check/readiness || echo "Readiness check failed"

# Test liveness endpoint
echo "Testing liveness endpoint..."
curl -s -w "Status: %{http_code}\n" http://localhost:8080/api/check/liveness || echo "Liveness check failed"

# Test startup endpoint
echo "Testing startup endpoint..."
curl -s -w "Status: %{http_code}\n" http://localhost:8080/api/check/startup || echo "Startup check failed"

echo ""
echo "Testing complete!"
echo "Note: Some endpoints may return 503 if no providers are registered, which is normal for minimal setup"
echo "Note: Version API not included in ultra-minimal build to avoid dependencies"

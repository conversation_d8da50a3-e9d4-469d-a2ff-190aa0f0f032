#!/bin/bash

# Test script for the minimal EDC connector

echo "Testing Minimal EDC Connector..."

# Wait for connector to start
echo "Waiting for connector to start..."
sleep 10

# Test health endpoint
echo "Testing health endpoint..."
curl -f http://localhost:8080/api/check/health || echo "Health check failed"

# Test readiness endpoint
echo "Testing readiness endpoint..."
curl -f http://localhost:8080/api/check/ready || echo "Readiness check failed"

# Test liveness endpoint
echo "Testing liveness endpoint..."
curl -f http://localhost:8080/api/check/live || echo "Liveness check failed"

# Test version endpoint
echo "Testing version endpoint..."
curl -f http://localhost:8181/api/version || echo "Version endpoint failed"

echo "Testing complete!"

#!/bin/bash

# Comprehensive test script for EDC connector endpoints

echo "=== Testing EDC Connector Endpoints ==="
echo ""

# Function to test an endpoint
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_codes="$3"  # Space-separated list of acceptable status codes
    
    echo "Testing $name..."
    echo "  URL: $url"
    
    # Get response with status code
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url" 2>/dev/null)
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    body=$(echo "$response" | sed 's/HTTPSTATUS:[0-9]*$//')
    
    if [ -z "$http_code" ]; then
        echo "  ❌ Failed to connect to $url"
        return 1
    fi
    
    # Check if status code is acceptable
    for code in $expected_codes; do
        if [ "$http_code" = "$code" ]; then
            echo "  ✅ Status: $http_code (expected)"
            if [ -n "$body" ] && [ "$body" != "null" ]; then
                echo "  📄 Response: $body"
            fi
            return 0
        fi
    done
    
    echo "  ❌ Status: $http_code (expected one of: $expected_codes)"
    if [ -n "$body" ]; then
        echo "  📄 Response: $body"
    fi
    return 1
}

# Wait for connector to start
echo "Waiting for connector to start..."
sleep 8

echo ""
echo "Testing observability endpoints..."

# Test health endpoint (should return 200)
test_endpoint "Health Check" "http://localhost:8080/api/check/health" "200"

# Test readiness endpoint (should return 200)
test_endpoint "Readiness Check" "http://localhost:8080/api/check/readiness" "200"

# Test liveness endpoint (may return 200 or 503 depending on providers)
test_endpoint "Liveness Check" "http://localhost:8080/api/check/liveness" "200 503"

# Test startup endpoint (should return 200)
test_endpoint "Startup Check" "http://localhost:8080/api/check/startup" "200"

echo ""
echo "=== Test Summary ==="
echo ""
echo "Expected behavior for minimal connector:"
echo "• Health: 200 OK (connector is healthy)"
echo "• Readiness: 200 OK (connector is ready)"
echo "• Liveness: 200 OK or 503 Service Unavailable (depends on liveness providers)"
echo "• Startup: 200 OK (connector has started)"
echo ""
echo "A 503 status for liveness is normal if no liveness providers are registered."
echo "This is expected behavior for the ultra-minimal connector setup."

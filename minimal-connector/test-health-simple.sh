#!/bin/bash

# Simple test to check if health endpoints work now

echo "=== Testing Health Endpoints (Simple) ==="
echo ""

# Build
echo "Building..."
./gradlew clean build -q

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"

# Start connector
echo "Starting connector..."
./gradlew run > health-simple.log 2>&1 &
CONNECTOR_PID=$!

# Wait for startup
echo "Waiting 10 seconds for startup..."
sleep 10

# Test endpoints
echo ""
echo "Testing endpoints:"

test_endpoint() {
    local name="$1"
    local url="$2"
    
    response=$(curl -s -w "%{http_code}" "$url" 2>/dev/null)
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        echo "✅ $name: $http_code OK"
    else
        echo "❌ $name: $http_code"
    fi
}

test_endpoint "Health" "http://localhost:8080/api/check/health"
test_endpoint "Readiness" "http://localhost:8080/api/check/readiness"
test_endpoint "Liveness" "http://localhost:8080/api/check/liveness"
test_endpoint "Startup" "http://localhost:8080/api/check/startup"

# Stop connector
echo ""
echo "Stopping connector..."
kill $CONNECTOR_PID 2>/dev/null
wait $CONNECTOR_PID 2>/dev/null

# Quick log check
echo ""
if grep -q "Health providers registered" health-simple.log; then
    echo "✅ Health providers were registered"
else
    echo "⚠️  Health provider registration not found in log"
fi

echo ""
echo "=== Test Complete ==="

# Cleanup
rm -f health-simple.log

<code_scheme name="Project" version="173">
  <JavaCodeStyleSettings>
    <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="99" />
    <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="99" />
    <option name="IMPORT_LAYOUT_TABLE">
      <value>
        <package name="" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="java" withSubpackages="true" static="false" />
        <package name="javax" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="" withSubpackages="true" static="true" />
      </value>
    </option>
  </JavaCodeStyleSettings>
  <JetCodeStyleSettings>
    <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
  </JetCodeStyleSettings>
  <SqlCodeStyleSettings version="6">
    <option name="KEYWORD_CASE" value="2" />
    <option name="TYPE_CASE" value="2" />
    <option name="ALIAS_CASE" value="2" />
  </SqlCodeStyleSettings>
  <codeStyleSettings language="JAVA">
    <option name="SPACE_WITHIN_ARRAY_INITIALIZER_BRACES" value="true" />
    <arrangement>
      <groups>
        <group>
          <type>GETTERS_AND_SETTERS</type>
          <order>KEEP</order>
        </group>
        <group>
          <type>OVERRIDDEN_METHODS</type>
          <order>KEEP</order>
        </group>
      </groups>
      <rules>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <FINAL>true</FINAL>
                <PUBLIC>true</PUBLIC>
                <STATIC>true</STATIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <FINAL>true</FINAL>
                <PROTECTED>true</PROTECTED>
                <STATIC>true</STATIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <FINAL>true</FINAL>
                <PACKAGE_PRIVATE>true</PACKAGE_PRIVATE>
                <STATIC>true</STATIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <FINAL>true</FINAL>
                <PRIVATE>true</PRIVATE>
                <STATIC>true</STATIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <PUBLIC>true</PUBLIC>
                <STATIC>true</STATIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <PROTECTED>true</PROTECTED>
                <STATIC>true</STATIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <PACKAGE_PRIVATE>true</PACKAGE_PRIVATE>
                <STATIC>true</STATIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <PRIVATE>true</PRIVATE>
                <STATIC>true</STATIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <INITIALIZER_BLOCK>true</INITIALIZER_BLOCK>
                <STATIC>true</STATIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <FINAL>true</FINAL>
                <PUBLIC>true</PUBLIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <FINAL>true</FINAL>
                <PROTECTED>true</PROTECTED>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <FINAL>true</FINAL>
                <PACKAGE_PRIVATE>true</PACKAGE_PRIVATE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <FINAL>true</FINAL>
                <PRIVATE>true</PRIVATE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <PUBLIC>true</PUBLIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <PROTECTED>true</PROTECTED>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <PACKAGE_PRIVATE>true</PACKAGE_PRIVATE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <FIELD>true</FIELD>
                <PRIVATE>true</PRIVATE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <FIELD>true</FIELD>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <INITIALIZER_BLOCK>true</INITIALIZER_BLOCK>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <CONSTRUCTOR>true</CONSTRUCTOR>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <METHOD>true</METHOD>
                <STATIC>true</STATIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <METHOD>true</METHOD>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <METHOD>true</METHOD>
                <PROTECTED>true</PROTECTED>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <METHOD>true</METHOD>
                <PRIVATE>true</PRIVATE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <ENUM>true</ENUM>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <INTERFACE>true</INTERFACE>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <CLASS>true</CLASS>
                <STATIC>true</STATIC>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <CLASS>true</CLASS>
            </match>
          </rule>
        </section>
      </rules>
    </arrangement>
  </codeStyleSettings>
  <codeStyleSettings language="kotlin">
    <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
  </codeStyleSettings>
</code_scheme>